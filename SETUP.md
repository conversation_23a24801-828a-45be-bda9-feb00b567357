# Brand Management System Setup Guide

## 🎯 Overview

This brand management system replaces the weather agent with a comprehensive RAG-powered brand analysis platform. It features:

- **URL Processing**: Automatic content extraction and embedding generation
- **Vector Search**: Semantic search through brand data using OpenAI embeddings
- **AI Assistant**: Conversational interface powered by Convex Agent
- **Real-time UI**: Live status tracking and responsive design
- **Complete RAG Pipeline**: From URL input to searchable knowledge base

## 🚀 Quick Start

### 1. Initialize Convex (Required)

```bash
# Start Convex development server
npx convex dev
```

This will:
- Create a new Convex project (if needed)
- Generate the required API types
- Set up the database schema
- Install the Convex Agent component

### 2. Configure Environment Variables

Set your OpenAI API key in the Convex dashboard:

```bash
npx convex env set OPENAI_API_KEY sk-your-openai-api-key-here
```

Or set it in your local environment:

```bash
# In .env.local
OPENAI_API_KEY=sk-your-openai-api-key-here
```

### 3. Start the Application

```bash
# In one terminal - Convex backend
npx convex dev

# In another terminal - Next.js frontend
npm run dev:frontend
```

Or use the combined command:

```bash
npm run dev
```

## 📋 System Architecture

### Database Schema

The system uses 4 main tables:

1. **`brands`** - Brand metadata and processing status
2. **`brand_content`** - Raw extracted content from URLs
3. **`brand_chunks`** - Content split into searchable chunks
4. **`brand_embeddings`** - Vector embeddings with search index

### Key Components

#### Backend (`convex/`)
- `brands.ts` - Core brand processing functions
- `agent.ts` - Convex Agent with brand management tools
- `schema.ts` - Database schema definition

#### Frontend (`components/`)
- `brand-management.tsx` - Main dashboard interface
- `brand-chat.tsx` - AI assistant chat interface

#### API Routes (`app/api/brands/`)
- `add/route.ts` - Add new brand URLs
- `search/route.ts` - Search brand data
- `status/route.ts` - Get brand processing status

## 🔧 Features

### 1. Brand URL Processing

**How it works:**
1. User enters a brand URL
2. System validates and queues for processing
3. Content is extracted from the webpage
4. Text is chunked for optimal embedding
5. OpenAI generates vector embeddings
6. Data is stored in Convex vector database

**Usage:**
- Enter URL in the dashboard form
- Or ask the AI: "Process https://example.com"

### 2. Semantic Search

**How it works:**
1. User query is converted to embeddings
2. Vector similarity search finds relevant content
3. Results ranked by relevance score
4. Context provided to AI assistant

**Usage:**
- Use the AI chat: "Search for e-commerce brands"
- Or: "Find brands that focus on sustainability"

### 3. AI Assistant

**Available Commands:**
- `processBrandUrl` - Add and process new brand URLs
- `searchBrands` - Semantic search through brand data
- `getBrandStatus` - Check processing status
- `listAllBrands` - View all brands with statistics

**Example Conversations:**
```
User: "Process https://shopify.com"
AI: "Brand URL https://shopify.com has been successfully added and queued for processing..."

User: "Search for brands that sell clothing"
AI: "Found 3 relevant brand results for query: 'brands that sell clothing'..."

User: "What's the status of shopify.com?"
AI: "Brand Status for https://shopify.com: Status: completed, Name: Shopify..."
```

## 🎨 User Interface

### Dashboard Features

1. **URL Input Form**
   - Real-time validation
   - Duplicate detection
   - Processing status feedback

2. **Total Brands Table**
   - Live status indicators
   - Clickable URLs
   - Error message display
   - Sorting and filtering

3. **Statistics Cards**
   - Pending, Processing, Completed, Failed counts
   - Visual status indicators
   - Real-time updates

4. **AI Chat Interface**
   - Natural language interaction
   - Tool integration
   - Message history
   - Typing indicators

### Status Indicators

- 🟡 **Pending** - Queued for processing
- 🔵 **Processing** - Currently extracting content
- 🟢 **Completed** - Ready for search
- 🔴 **Failed** - Error occurred (with details)

## 🔍 Advanced Usage

### Programmatic API

```javascript
// Add a brand
const response = await fetch('/api/brands/add', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ url: 'https://example.com' })
});

// Search brands
const results = await fetch('/api/brands/search', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ query: 'e-commerce', limit: 5 })
});
```

### Direct Convex Integration

```javascript
import { api } from './convex/_generated/api';

// In a Convex function
const brandId = await ctx.runMutation(api.brands.addBrand, { 
  url: 'https://example.com' 
});

const results = await ctx.runAction(api.brands.searchBrands, { 
  query: 'sustainability', 
  limit: 10 
});
```

## 🚧 Future Enhancements

The system is architected for easy extension:

### Planned Features
- **Firecrawl Integration** - Enhanced content extraction
- **Bulk Processing** - Multiple URL upload
- **Advanced Analytics** - Brand comparison and insights
- **Export Features** - Data export and reporting
- **User Management** - Multi-user support with permissions

### Integration Points
- Content extraction pipeline (easily replaceable)
- Embedding model (configurable)
- Search algorithms (extensible)
- UI components (modular)

## 🐛 Troubleshooting

### Common Issues

1. **"Cannot find module 'components'"**
   - Run `npx convex dev` to generate types
   - Ensure Convex Agent component is installed

2. **"OpenAI API error"**
   - Check API key is set correctly
   - Verify API key has sufficient credits
   - Ensure key has embedding permissions

3. **"Brand already exists"**
   - Check the brands table for duplicates
   - Use different URL or update existing entry

4. **Search returns no results**
   - Ensure brands are in "completed" status
   - Try different search terms
   - Check embedding generation succeeded

### Debug Commands

```bash
# Check Convex status
npx convex dashboard

# View logs
npx convex logs

# Reset database (careful!)
npx convex run brands:listBrands
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Convex documentation: https://docs.convex.dev
3. Check OpenAI API status: https://status.openai.com

## 🎉 Success!

Once setup is complete, you'll have a fully functional brand management system with:
- ✅ Automatic content processing
- ✅ Vector-powered search
- ✅ AI assistant integration
- ✅ Real-time dashboard
- ✅ Scalable architecture

Ready to manage and analyze brands at scale!
