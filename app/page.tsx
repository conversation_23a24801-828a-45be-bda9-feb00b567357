"use client";

import { CsvUpload } from "@/components/csv-upload";
import { ProcessingCard } from "@/components/processing-card";
import { BrandTable } from "@/components/brand-table";
import { ProductTable } from "@/components/product-table";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <h1 className="text-2xl font-bold">Shopwiz Brand Management</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 py-6 space-y-6">
        {/* CSV Upload */}
        <CsvUpload />

        {/* Processing Status */}
        <ProcessingCard />

        {/* Data Tables */}
        <Tabs defaultValue="brands" className="space-y-4">
          <TabsList>
            <TabsTrigger value="brands">Brands</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
          </TabsList>

          <TabsContent value="brands">
            <BrandTable />
          </TabsContent>

          <TabsContent value="products">
            <ProductTable />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}

