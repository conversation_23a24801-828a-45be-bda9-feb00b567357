"use client";

import { useState } from "react";
import { CsvUpload } from "@/components/csv-upload";
import { ProcessingCard } from "@/components/processing-card";
import { BrandTable } from "@/components/brand-table";
import { ProductTable } from "@/components/product-table";

export default function Home() {
  const [activeTab, setActiveTab] = useState<"brands" | "products">("brands");

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <h1 className="text-2xl font-bold text-gray-900">Shopwiz Brand Management</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 py-6 space-y-6">
        {/* CSV Upload */}
        <CsvUpload />

        {/* Processing Status */}
        <ProcessingCard />

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab("brands")}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "brands"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                Brands
              </button>
              <button
                onClick={() => setActiveTab("products")}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "products"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                Products
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === "brands" && <BrandTable />}
            {activeTab === "products" && <ProductTable />}
          </div>
        </div>
      </main>
    </div>
  );
}

