"use client";

import { CsvUpload } from "@/components/csv-upload";
import { ProcessingCard } from "@/components/processing-card";
import { BrandTable } from "@/components/brand-table";
import { ProductTable } from "@/components/product-table";
import { DarkModeToggle } from "@/components/dark-mode-toggle";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function Home() {
  return (
    <div className="min-h-screen bg-background dark:bg-gray-900">
      <header className="border-b border-border dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-foreground dark:text-white">Shopwiz Brand Management</h1>
          <DarkModeToggle />
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 py-6 space-y-6">
        {/* CSV Upload */}
        <CsvUpload />

        {/* Processing Status */}
        <ProcessingCard />

        {/* Data Tables */}
        <Tabs defaultValue="brands" className="space-y-4">
          <TabsList>
            <TabsTrigger value="brands">Brands</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
          </TabsList>

          <TabsContent value="brands">
            <BrandTable />
          </TabsContent>

          <TabsContent value="products">
            <ProductTable />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}

