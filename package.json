{"name": "supershopai", "version": "0.1.0", "private": true, "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "next dev", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dashboard", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@convex-dev/agent": "^0.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "lucide-react": "^0.513.0", "next": "15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.7", "eslint": "^9", "eslint-config-next": "15.2.3", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}