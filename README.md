# 🏢 Brand Management System

A comprehensive RAG-powered brand analysis platform built with **Next.js**, **Convex**, and **OpenAI**. This system replaces the previous weather agent with advanced brand processing, semantic search, and AI-powered insights.

## ✨ Features

### 🔄 **Complete RAG Pipeline**
- **URL Processing**: Automatic content extraction from brand websites
- **Vector Embeddings**: OpenAI-powered semantic understanding
- **Semantic Search**: Find relevant brand information using natural language
- **Real-time Processing**: Live status updates and reactive UI

### 🤖 **AI Assistant**
- **Conversational Interface**: Natural language brand management
- **Tool Integration**: Process URLs, search data, check status
- **Context Awareness**: Maintains conversation history
- **Smart Responses**: Actionable insights and recommendations

### 📊 **Dashboard**
- **URL Input**: Simple form to add brand websites
- **Status Tracking**: Visual indicators for processing stages
- **Data Table**: Complete brand overview with sorting/filtering
- **Statistics**: Real-time metrics and status breakdown

## Get started

If you just cloned this codebase and didn't use `npm create convex`, run:

```
npm install
npm run dev
```

If you're reading this README on GitHub and want to use this template, run:

```
npm create convex@latest -- -t nextjs
```

## Learn more

To learn more about developing your project with Convex, check out:

- The [Tour of Convex](https://docs.convex.dev/get-started) for a thorough introduction to Convex principles.
- The rest of [Convex docs](https://docs.convex.dev/) to learn about all Convex features.
- [Stack](https://stack.convex.dev/) for in-depth articles on advanced topics.

## Join the community

Join thousands of developers building full-stack apps with Convex:

- Join the [Convex Discord community](https://convex.dev/community) to get help in real-time.
- Follow [Convex on GitHub](https://github.com/get-convex/), star and contribute to the open-source implementation of Convex.
