"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { Doc } from "../convex/_generated/dataModel";
import { ProductDataTable } from "./product-data-table";
import { CsvUpload } from "./csv-upload";
import { ProductScraperComponent } from "./product-scraper";

export function BrandManagementComponent() {
  const [url, setUrl] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState("");
  const [activeTab, setActiveTab] = useState<"brands" | "products" | "scraper">("brands");

  // Query to get all brands
  const brands = useQuery(api.brands.listBrands);

  // Query to get product stats
  const productStats = useQuery(api.products.getProductStats);

  // Mutation to add a new brand
  const addBrand = useMutation(api.brands.addBrand);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!url.trim()) {
      setMessage("Please enter a valid URL");
      return;
    }

    setIsSubmitting(true);
    setMessage("");

    try {
      // Validate URL format
      new URL(url);
      
      await addBrand({ url: url.trim() });
      setUrl("");
      setMessage("Brand URL added successfully and queued for processing!");
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes("Invalid URL")) {
          setMessage("Please enter a valid URL format");
        } else if (error.message.includes("already exists")) {
          setMessage("This brand URL has already been added");
        } else {
          setMessage(`Error: ${error.message}`);
        }
      } else {
        setMessage("An unexpected error occurred");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600 bg-green-100";
      case "processing":
        return "text-blue-600 bg-blue-100";
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "failed":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return "✓";
      case "processing":
        return "⟳";
      case "pending":
        return "⏳";
      case "failed":
        return "✗";
      default:
        return "?";
    }
  };

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Brand & Product Monitoring System
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Monitor 100k+ brands and their products with advanced search and analytics
        </p>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab("brands")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "brands"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              Brands ({brands?.length || 0})
            </button>
            <button
              onClick={() => setActiveTab("products")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "products"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              Products ({productStats?.total || 0})
            </button>
            <button
              onClick={() => setActiveTab("scraper")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "scraper"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              Product Scraper
            </button>
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === "brands" && (
        <>
          {/* URL Input Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
              Add Single Brand
            </h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Brand Website URL
                </label>
                <input
                  type="url"
                  id="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="https://example.com"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  disabled={isSubmitting}
                />
              </div>
              <div className="flex items-center gap-4">
                <button
                  type="submit"
                  disabled={isSubmitting || !url.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? "Processing..." : "Add Brand"}
                </button>
                {message && (
                  <p className={`text-sm ${message.includes("Error") || message.includes("already") ? "text-red-600" : "text-green-600"}`}>
                    {message}
                  </p>
                )}
              </div>
            </form>
          </div>

          {/* CSV Upload */}
          <CsvUpload />

          {/* Brands Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
              All Brands
            </h2>

            {brands === undefined ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600 dark:text-gray-400">Loading brands...</span>
              </div>
            ) : brands.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No brands added yet. Add your first brand URL above or upload a CSV file!
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Brand
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Industry
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Products
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Last Updated
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {brands.map((brand: Doc<"brands">) => (
                      <tr key={brand._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            {brand.logo && (
                              <img
                                src={brand.logo}
                                alt={brand.name || "Brand logo"}
                                className="h-8 w-8 rounded-lg object-cover mr-3"
                              />
                            )}
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {brand.name || brand.url}
                              </div>
                              <a
                                href={brand.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                              >
                                {brand.url}
                              </a>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {brand.industry || "—"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {brand.productCount || 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(brand.status)}`}>
                            <span className="mr-1">{getStatusIcon(brand.status)}</span>
                            {brand.status}
                          </span>
                          {brand.status === "failed" && brand.errorMessage && (
                            <div className="mt-1 text-xs text-red-600 dark:text-red-400">
                              {brand.errorMessage}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {new Date(brand.updatedAt).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Brand Statistics */}
          {brands && brands.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {["pending", "processing", "completed", "failed"].map((status) => {
                const count = brands.filter((brand: Doc<"brands">) => brand.status === status).length;
                return (
                  <div key={status} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-full ${getStatusColor(status)}`}>
                        {getStatusIcon(status)}
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 capitalize">
                          {status}
                        </p>
                        <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                          {count}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </>
      )}

      {activeTab === "products" && (
        <ProductDataTable brands={brands} />
      )}

      {activeTab === "scraper" && (
        <ProductScraperComponent />
      )}

    </div>
  );
}
