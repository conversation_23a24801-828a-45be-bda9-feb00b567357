"use client";

import { useState } from "react";
import { useAction, useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";

export function ProductScraperComponent() {
  const [isScrapingAll, setIsScrapingAll] = useState(false);
  const [scrapingResults, setScrapingResults] = useState<any>(null);
  const [selectedBrandIds, setSelectedBrandIds] = useState<Id<"brands">[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Actions and queries
  const scrapeSelectedBrands = useAction(api.product_scraper.scrapeSelectedBrands);
  const scrapeAllPending = useAction(api.product_scraper.scrapeProductsForAllPendingBrands);
  const scrapingStats = useQuery(api.product_scraper.getProductScrapingStats);
  const recentActivity = useQuery(api.product_scraper.getRecentProductActivity, { limit: 10 });
  
  // Get brands that need product scraping
  const brandsNeedingScraping = useQuery(api.brands.listBrands, {
    status: "completed",
    limit: 100,
  });

  const handleScrapeAll = async () => {
    try {
      setIsScrapingAll(true);
      setError(null);
      
      const result = await scrapeAllPending({ limit: 50 });
      setScrapingResults(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to scrape products");
    } finally {
      setIsScrapingAll(false);
    }
  };

  const handleScrapeSelected = async () => {
    if (selectedBrandIds.length === 0) {
      setError("Please select at least one brand");
      return;
    }

    try {
      setIsScrapingAll(true);
      setError(null);
      
      const result = await scrapeSelectedBrands({ brandIds: selectedBrandIds });
      setScrapingResults(result);
      setSelectedBrandIds([]);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to scrape products");
    } finally {
      setIsScrapingAll(false);
    }
  };

  const toggleBrandSelection = (brandId: Id<"brands">) => {
    setSelectedBrandIds(prev => 
      prev.includes(brandId) 
        ? prev.filter(id => id !== brandId)
        : [...prev, brandId]
    );
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Product Scraper
        </h2>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Fetch products from brand /products.json endpoints
        </div>
      </div>

      {/* Statistics */}
      {scrapingStats && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {formatNumber(scrapingStats.totalBrands)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Brands</div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {formatNumber(scrapingStats.totalProducts)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Products</div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {formatNumber(scrapingStats.brandsWithProducts)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Brands with Products</div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {formatNumber(scrapingStats.brandsNeedingScraping)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Need Scraping</div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
              {formatNumber(scrapingStats.averageProductsPerBrand)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Avg Products/Brand</div>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Scraping Actions
        </h3>
        
        <div className="flex flex-wrap gap-4">
          <button
            onClick={handleScrapeAll}
            disabled={isScrapingAll || !scrapingStats?.brandsNeedingScraping}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {isScrapingAll ? "Scraping..." : `Scrape All Pending (${scrapingStats?.brandsNeedingScraping || 0})`}
          </button>
          
          <button
            onClick={handleScrapeSelected}
            disabled={isScrapingAll || selectedBrandIds.length === 0}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            {isScrapingAll ? "Scraping..." : `Scrape Selected (${selectedBrandIds.length})`}
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-800 dark:text-red-200">{error}</p>
          </div>
        )}

        {/* Results Display */}
        {scrapingResults && (
          <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">
              ✅ Scraping Completed
            </h4>
            <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
              <p>• Total brands processed: {scrapingResults.totalBrands}</p>
              <p>• Successful: {scrapingResults.summary.successful}</p>
              <p>• Failed: {scrapingResults.summary.failed}</p>
            </div>
          </div>
        )}
      </div>

      {/* Brands Needing Scraping */}
      {brandsNeedingScraping && brandsNeedingScraping.length > 0 && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Brands Needing Product Scraping ({brandsNeedingScraping.length})
          </h3>
          
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {brandsNeedingScraping.map((brand) => (
              <div
                key={brand._id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={selectedBrandIds.includes(brand._id)}
                    onChange={() => toggleBrandSelection(brand._id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {brand.name || "Unknown Brand"}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {brand.url}
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Products: {brand.productCount || 0}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Activity */}
      {recentActivity && recentActivity.length > 0 && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Recent Product Activity
          </h3>
          
          <div className="space-y-2">
            {recentActivity.map((product) => (
              <div
                key={product._id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {product.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {product.category} • ${product.price}
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {product.lastScrapedAt ? new Date(product.lastScrapedAt).toLocaleString() : "Never"}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
