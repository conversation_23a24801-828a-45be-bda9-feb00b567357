"use client";

import { useState, useRef } from "react";
import { useAction, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export function CsvUpload() {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<{
    batchId: string;
    totalUrls: number;
    newBrands: number;
    duplicates: number;
    message: string;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Action to process CSV
  const processCsvFile = useAction(api.csv_import.processCsvFile);

  // Mutation to download sample CSV
  const downloadSampleCsv = useMutation(api.csv_import.downloadSampleCsv);



  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    const csvFile = files.find(file => file.type === "text/csv" || file.name.endsWith(".csv"));
    
    if (csvFile) {
      handleFileUpload(csvFile);
    } else {
      setError("Please upload a CSV file");
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file: File) => {
    setIsUploading(true);
    setError(null);
    setUploadResult(null);

    try {
      // Read file content
      const text = await file.text();
      
      // Validate CSV format
      const lines = text.trim().split('\n');
      if (lines.length < 2) {
        throw new Error("CSV file must have at least a header row and one data row");
      }

      // Process the CSV
      const result = await processCsvFile({
        filename: file.name,
        csvData: text,
      });

      setUploadResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to process CSV file");
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const handleDownloadSample = async () => {
    try {
      const sample = await downloadSampleCsv();
      const blob = new Blob([sample.content], { type: sample.mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = sample.filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch {
      setError("Failed to download sample CSV");
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>CSV Brand Upload</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            isDragging
              ? "border-primary bg-primary/5"
              : "border-muted-foreground/25 hover:border-muted-foreground/50"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="space-y-4">
            <div className="text-4xl">📄</div>
            <div>
              <p className="text-lg font-medium">
                Drop your CSV file here
              </p>
              <p className="text-sm text-muted-foreground">
                or click to browse
              </p>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="hidden"
            />

            <Button
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              {isUploading ? "Processing..." : "Select CSV File"}
            </Button>
          </div>
        </div>

        {/* CSV Format Instructions */}
        <div className="p-4 bg-muted/50 rounded-lg">
          <div className="flex justify-between items-start mb-2">
            <h4 className="font-medium">
              CSV Format Requirements:
            </h4>
            <Button
              onClick={handleDownloadSample}
              size="sm"
              variant="outline"
            >
              Download Sample
            </Button>
          </div>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• <strong>Simple format:</strong> One URL per line (no headers needed)</li>
            <li>• <strong>Or with headers:</strong> Column named &quot;url&quot;, &quot;website&quot;, or &quot;domain&quot;</li>
            <li>• URLs can be with or without https:// prefix</li>
            <li>• Duplicates will be automatically detected and skipped</li>
            <li>• Maximum 10,000 URLs per file for optimal performance</li>
          </ul>

          <div className="mt-3">
            <p className="text-sm font-medium">Simple format example:</p>
            <code className="text-xs bg-background p-2 rounded mt-1 block">
              https://shopify.com<br/>
              stripe.com<br/>
              notion.so<br/>
              figma.com
            </code>
          </div>
        </div>

        {/* Upload Result */}
        {uploadResult && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-800 mb-2">
              ✅ Upload Successful
            </h4>
            <div className="text-sm text-green-700 space-y-1">
              <p>• Batch ID: {uploadResult.batchId}</p>
              <p>• Total URLs processed: {formatNumber(uploadResult.totalUrls)}</p>
              <p>• New brands added: {formatNumber(uploadResult.newBrands)}</p>
              <p>• Duplicates found: {formatNumber(uploadResult.duplicates)}</p>
              <p>• Status: {uploadResult.newBrands > 0 ? 'Processing started for new brands' : 'All brands were duplicates'}</p>
            </div>
          </div>
        )}

        {/* Error */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <h4 className="font-medium text-red-800 mb-2">
              ❌ Upload Failed
            </h4>
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
