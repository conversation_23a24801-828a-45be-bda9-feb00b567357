"use client";

import { useState, useRef } from "react";
import { useAction, useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { Doc } from "../convex/_generated/dataModel";

export function CsvUpload() {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<{
    batchId: string;
    totalUrls: number;
    newBrands: number;
    duplicates: number;
    message: string;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Action to process CSV
  const processCsvFile = useAction(api.csv_import.processCsvFile);

  // Mutation to download sample CSV
  const downloadSampleCsv = useMutation(api.csv_import.downloadSampleCsv);

  // Query import stats
  const importStats = useQuery(api.csv_import.getImportStats);

  // Query recent batches
  const recentBatches = useQuery(api.csv_import.listImportBatches, { limit: 5 });

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    const csvFile = files.find(file => file.type === "text/csv" || file.name.endsWith(".csv"));
    
    if (csvFile) {
      handleFileUpload(csvFile);
    } else {
      setError("Please upload a CSV file");
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file: File) => {
    setIsUploading(true);
    setError(null);
    setUploadResult(null);

    try {
      // Read file content
      const text = await file.text();
      
      // Validate CSV format
      const lines = text.trim().split('\n');
      if (lines.length < 2) {
        throw new Error("CSV file must have at least a header row and one data row");
      }

      // Process the CSV
      const result = await processCsvFile({
        filename: file.name,
        csvData: text,
      });

      setUploadResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to process CSV file");
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const handleDownloadSample = async () => {
    try {
      const sample = await downloadSampleCsv();
      const blob = new Blob([sample.content], { type: sample.mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = sample.filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      setError("Failed to download sample CSV");
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
          Bulk Brand Import
        </h3>
        
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            isDragging
              ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
              : "border-gray-300 dark:border-gray-600 hover:border-gray-400"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="space-y-4">
            <div className="text-4xl text-gray-400">📄</div>
            <div>
              <p className="text-lg font-medium text-gray-900 dark:text-white">
                Drop your CSV file here
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                or click to browse
              </p>
            </div>
            
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="hidden"
            />
            
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isUploading ? "Processing..." : "Select CSV File"}
            </button>
          </div>
        </div>

        {/* CSV Format Instructions */}
        <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex justify-between items-start mb-2">
            <h4 className="font-medium text-gray-900 dark:text-white">
              CSV Format Requirements:
            </h4>
            <button
              onClick={handleDownloadSample}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Download Sample
            </button>
          </div>
          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <li>• <strong>Simple format:</strong> One URL per line (no headers needed)</li>
            <li>• <strong>Or with headers:</strong> Column named &quot;url&quot;, &quot;website&quot;, or &quot;domain&quot;</li>
            <li>• URLs can be with or without https:// prefix</li>
            <li>• Duplicates will be automatically detected and skipped</li>
            <li>• Maximum 10,000 URLs per file for optimal performance</li>
          </ul>

          <div className="mt-3">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Simple format example:</p>
            <code className="text-xs bg-white dark:bg-gray-800 p-2 rounded mt-1 block">
              https://shopify.com<br/>
              stripe.com<br/>
              notion.so<br/>
              figma.com
            </code>
          </div>
        </div>

        {/* Upload Result */}
        {uploadResult && (
          <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">
              ✅ Upload Successful
            </h4>
            <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
              <p>• Batch ID: {uploadResult.batchId}</p>
              <p>• Total URLs processed: {formatNumber(uploadResult.totalUrls)}</p>
              <p>• New brands added: {formatNumber(uploadResult.newBrands)}</p>
              <p>• Duplicates found: {formatNumber(uploadResult.duplicates)}</p>
              <p>• Status: {uploadResult.newBrands > 0 ? 'Processing started for new brands' : 'All brands were duplicates'}</p>
            </div>
          </div>
        )}

        {/* Error */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">
              ❌ Upload Failed
            </h4>
            <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}
      </div>

      {/* Import Statistics */}
      {importStats && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            Import Statistics
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatNumber(importStats.totalBatches)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Total Batches
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatNumber(importStats.totalUrlsProcessed)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                URLs Processed
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatNumber(importStats.totalSuccessful)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Successful
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {formatNumber(importStats.totalFailed)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Failed
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {formatNumber(importStats.activeBatches)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Active Batches
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {formatNumber(importStats.completedBatches)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Completed
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Batches */}
      {recentBatches && recentBatches.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            Recent Import Batches
          </h3>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Filename
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Success Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Created
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {recentBatches.map((batch: Doc<"import_batches">) => {
                  const progressPercent = batch.totalRows > 0 ? (batch.processedRows / batch.totalRows) * 100 : 0;
                  const successRate = batch.processedRows > 0 ? (batch.successfulRows / batch.processedRows) * 100 : 0;
                  
                  return (
                    <tr key={batch._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {batch.filename}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          batch.status === "completed" 
                            ? "text-green-600 bg-green-100" 
                            : batch.status === "failed"
                            ? "text-red-600 bg-red-100"
                            : "text-yellow-600 bg-yellow-100"
                        }`}>
                          {batch.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {batch.processedRows} / {batch.totalRows} ({progressPercent.toFixed(1)}%)
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {successRate.toFixed(1)}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {new Date(batch.createdAt).toLocaleDateString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
