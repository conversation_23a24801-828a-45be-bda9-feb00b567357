"use client";

import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

export function ProcessingCard() {
  const importStats = useQuery(api.csv_import.getImportStats);
  const recentBatches = useQuery(api.csv_import.listImportBatches, { limit: 3 });

  if (!importStats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Processing Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-1/4"></div>
            <div className="h-8 bg-muted rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const progressPercentage = importStats.totalUrlsProcessed > 0
    ? Math.round((importStats.totalSuccessful / importStats.totalUrlsProcessed) * 100)
    : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Processing Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Overall Progress</span>
            <span>{importStats.totalSuccessful} / {importStats.totalUrlsProcessed} processed</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          <div className="text-xs text-muted-foreground">{progressPercentage}% complete</div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold text-blue-600">{importStats.totalBatches}</div>
            <div className="text-sm text-muted-foreground">Total Batches</div>
          </div>
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold text-green-600">{importStats.totalSuccessful}</div>
            <div className="text-sm text-muted-foreground">Successful</div>
          </div>
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold text-red-600">{importStats.totalFailed}</div>
            <div className="text-sm text-muted-foreground">Failed</div>
          </div>
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold text-orange-600">{importStats.activeBatches}</div>
            <div className="text-sm text-muted-foreground">Active</div>
          </div>
        </div>

        {/* Recent Batches */}
        {recentBatches && recentBatches.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-sm font-medium">Recent Batches</h3>
            <div className="space-y-2">
              {recentBatches.map((batch) => (
                <div key={batch._id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="space-y-1">
                    <div className="text-sm font-medium">{batch.filename}</div>
                    <div className="text-xs text-muted-foreground">
                      {batch.processedRows} / {batch.totalRows} processed
                    </div>
                  </div>
                  <Badge variant={
                    batch.status === "completed" ? "default" :
                    batch.status === "processing" ? "secondary" : "destructive"
                  }>
                    {batch.status}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
