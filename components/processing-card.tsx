"use client";

import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";

export function ProcessingCard() {
  const importStats = useQuery(api.csv_import.getImportStats);
  const recentBatches = useQuery(api.csv_import.listImportBatches, { limit: 3 });

  if (!importStats) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-full"></div>
        </div>
      </div>
    );
  }

  const progressPercentage = importStats.totalUrlsProcessed > 0 
    ? Math.round((importStats.totalSuccessful / importStats.totalUrlsProcessed) * 100)
    : 0;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Processing Status</h2>
      
      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Overall Progress</span>
          <span>{importStats.totalSuccessful} / {importStats.totalUrlsProcessed} processed</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-500 mt-1">{progressPercentage}% complete</div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{importStats.totalBatches}</div>
          <div className="text-sm text-gray-500">Total Batches</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{importStats.totalSuccessful}</div>
          <div className="text-sm text-gray-500">Successful</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">{importStats.totalFailed}</div>
          <div className="text-sm text-gray-500">Failed</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">{importStats.activeBatches}</div>
          <div className="text-sm text-gray-500">Active</div>
        </div>
      </div>

      {/* Recent Batches */}
      {recentBatches && recentBatches.length > 0 && (
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-3">Recent Batches</h3>
          <div className="space-y-2">
            {recentBatches.map((batch) => (
              <div key={batch._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-gray-900">{batch.filename}</div>
                  <div className="text-xs text-gray-500">
                    {batch.processedRows} / {batch.totalRows} processed
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    batch.status === "completed" 
                      ? "bg-green-100 text-green-800"
                      : batch.status === "processing"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-red-100 text-red-800"
                  }`}>
                    {batch.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
