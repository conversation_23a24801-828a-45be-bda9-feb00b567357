import { v } from "convex/values";
import { mutation, query, action, internalMutation, internalAction } from "./_generated/server";
import { internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// Query to get all brands
export const listBrands = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("brands")
      .order("desc")
      .collect();
  },
});

// Query to get brands by status
export const getBrandsByStatus = query({
  args: {
    status: v.union(v.literal("pending"), v.literal("processing"), v.literal("completed"), v.literal("failed")),
  },
  handler: async (ctx, { status }) => {
    return await ctx.db
      .query("brands")
      .withIndex("byStatus", (q) => q.eq("status", status))
      .order("desc")
      .collect();
  },
});

// Mutation to add a new brand URL
export const addBrand = mutation({
  args: {
    url: v.string(),
  },
  handler: async (ctx, { url }) => {
    // Validate URL format
    try {
      new URL(url);
    } catch {
      throw new Error("Invalid URL format");
    }

    // Check if brand already exists
    const existing = await ctx.db
      .query("brands")
      .withIndex("byUrl", (q) => q.eq("url", url))
      .first();

    if (existing) {
      throw new Error("Brand URL already exists");
    }

    const now = Date.now();
    const brandId = await ctx.db.insert("brands", {
      url,
      status: "pending",
      createdAt: now,
      updatedAt: now,
      retryCount: 0,
      productCount: 0,
    });

    // Schedule processing
    await ctx.scheduler.runAfter(0, internal.brands.processBrand, {
      brandId,
    });

    return brandId;
  },
});

// Create brand from CSV import
export const createBrandFromImport = mutation({
  args: {
    url: v.string(),
    batchId: v.id("import_batches"),
  },
  handler: async (ctx, { url, batchId }) => {
    const now = Date.now();
    return await ctx.db.insert("brands", {
      url,
      status: "pending",
      createdAt: now,
      updatedAt: now,
      retryCount: 0,
      productCount: 0,
      importBatchId: batchId,
    });
  },
});

// Update brand batch ID
export const updateBrandBatch = mutation({
  args: {
    brandId: v.id("brands"),
    batchId: v.id("import_batches"),
  },
  handler: async (ctx, { brandId, batchId }) => {
    await ctx.db.patch(brandId, {
      importBatchId: batchId,
      updatedAt: Date.now(),
    });
  },
});

// Internal action to process a brand URL
export const processBrand = internalAction({
  args: {
    brandId: v.id("brands"),
  },
  handler: async (ctx, { brandId }) => {
    try {
      // Update status to processing
      await ctx.runMutation(internal.brands.updateBrandStatus, {
        brandId,
        status: "processing",
      });

      // Get brand details
      const brand = await ctx.runQuery(internal.brands.getBrand, { brandId });
      if (!brand) {
        throw new Error("Brand not found");
      }

      // Extract content from URL
      const content = await extractContentFromUrl(brand.url);
      
      // Store content
      const contentId = await ctx.runMutation(internal.brands.storeBrandContent, {
        brandId,
        content,
      });

      // Create chunks and generate embeddings
      await ctx.runAction(internal.brands.processContentChunks, {
        brandId,
        contentId,
        content: content.rawContent,
      });

      // Update status to completed
      await ctx.runMutation(internal.brands.updateBrandStatus, {
        brandId,
        status: "completed",
      });

    } catch (error) {
      console.error("Error processing brand:", error);
      await ctx.runMutation(internal.brands.updateBrandStatus, {
        brandId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },
});

// Internal query to get a single brand
export const getBrand = query({
  args: {
    brandId: v.id("brands"),
  },
  handler: async (ctx, { brandId }) => {
    return await ctx.db.get(brandId);
  },
});

// Internal mutation to update brand status
export const updateBrandStatus = internalMutation({
  args: {
    brandId: v.id("brands"),
    status: v.union(v.literal("pending"), v.literal("processing"), v.literal("completed"), v.literal("failed")),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, { brandId, status, errorMessage }) => {
    await ctx.db.patch(brandId, {
      status,
      updatedAt: Date.now(),
      ...(errorMessage && { errorMessage }),
    });
  },
});

// Internal mutation to store brand content
export const storeBrandContent = internalMutation({
  args: {
    brandId: v.id("brands"),
    content: v.object({
      rawContent: v.string(),
      title: v.optional(v.string()),
      description: v.optional(v.string()),
    }),
  },
  handler: async (ctx, { brandId, content }) => {
    return await ctx.db.insert("brand_content", {
      brandId,
      contentType: "homepage",
      rawContent: content.rawContent,
      title: content.title,
      description: content.description,
      extractedAt: Date.now(),
    });
  },
});

// Internal action to process content chunks and generate embeddings
export const processContentChunks = internalAction({
  args: {
    brandId: v.id("brands"),
    contentId: v.id("brand_content"),
    content: v.string(),
  },
  handler: async (ctx, { brandId, contentId, content }) => {
    // Split content into chunks
    const chunks = splitTextIntoChunks(content, 1000, 100);

    // Store chunks
    const chunkIds: Id<"brand_chunks">[] = [];
    for (let i = 0; i < chunks.length; i++) {
      const chunkId = await ctx.runMutation(internal.brands.storeBrandChunk, {
        brandId,
        contentId,
        text: chunks[i],
        chunkIndex: i,
      });
      chunkIds.push(chunkId);
    }

    // Generate embeddings for chunks
    if (chunks.length > 0) {
      const embeddings = await generateEmbeddings(chunks);

      // Store embeddings
      for (let i = 0; i < embeddings.length; i++) {
        const embeddingId = await ctx.runMutation(internal.brands.storeBrandEmbedding, {
          chunkId: chunkIds[i],
          embedding: embeddings[i],
          model: "text-embedding-ada-002",
        });

        // Update chunk with embedding ID
        await ctx.runMutation(internal.brands.updateChunkEmbedding, {
          chunkId: chunkIds[i],
          embeddingId,
        });
      }
    }
  },
});

// Internal mutation to store brand chunk
export const storeBrandChunk = internalMutation({
  args: {
    brandId: v.id("brands"),
    contentId: v.id("brand_content"),
    text: v.string(),
    chunkIndex: v.number(),
  },
  handler: async (ctx, { brandId, contentId, text, chunkIndex }) => {
    return await ctx.db.insert("brand_chunks", {
      brandId,
      contentId,
      text,
      chunkIndex,
      chunkType: "content",
      embeddingId: null,
    });
  },
});

// Internal mutation to store brand embedding
export const storeBrandEmbedding = internalMutation({
  args: {
    chunkId: v.id("brand_chunks"),
    embedding: v.array(v.number()),
    model: v.string(),
  },
  handler: async (ctx, { chunkId, embedding, model }) => {
    return await ctx.db.insert("brand_embeddings", {
      chunkId,
      embedding,
      model,
      dimensions: embedding.length,
      createdAt: Date.now(),
    });
  },
});

// Internal mutation to update chunk with embedding ID
export const updateChunkEmbedding = internalMutation({
  args: {
    chunkId: v.id("brand_chunks"),
    embeddingId: v.id("brand_embeddings"),
  },
  handler: async (ctx, { chunkId, embeddingId }) => {
    await ctx.db.patch(chunkId, { embeddingId });
  },
});

// Helper function to extract content from URL
async function extractContentFromUrl(url: string) {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();

    // Basic content extraction (can be enhanced with more sophisticated parsing)
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const title = titleMatch ? titleMatch[1].trim() : undefined;

    const descriptionMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["'][^>]*>/i);
    const description = descriptionMatch ? descriptionMatch[1].trim() : undefined;

    // Remove HTML tags and extract text content
    const textContent = html
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]+>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    return {
      rawContent: textContent,
      title,
      description,
    };
  } catch (error) {
    throw new Error(`Failed to extract content from URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to split text into chunks
function splitTextIntoChunks(text: string, chunkSize: number, overlap: number): string[] {
  const chunks: string[] = [];
  let start = 0;

  while (start < text.length) {
    const end = Math.min(start + chunkSize, text.length);
    const chunk = text.slice(start, end);
    chunks.push(chunk);

    if (end === text.length) break;
    start = end - overlap;
  }

  return chunks;
}

// Query to get brand by URL
export const getBrandByUrl = query({
  args: {
    url: v.string(),
  },
  handler: async (ctx, { url }) => {
    return await ctx.db
      .query("brands")
      .withIndex("byUrl", (q) => q.eq("url", url))
      .first();
  },
});

// Action to search brands using vector search
export const searchBrands = action({
  args: {
    query: v.string(),
    limit: v.number(),
  },
  handler: async (ctx, { query, limit }) => {
    try {
      // Generate embedding for the search query
      const [queryEmbedding] = await generateEmbeddings([query]);

      // Perform vector search
      const searchResults = await ctx.vectorSearch("brand_embeddings", "byEmbedding", {
        vector: queryEmbedding,
        limit,
      });

      // Get the corresponding chunks and brands
      const results = [];
      for (const result of searchResults) {
        const embedding = await ctx.runQuery(internal.brands.getBrandEmbedding, {
          embeddingId: result._id,
        });

        if (embedding) {
          const chunk = await ctx.runQuery(internal.brands.getBrandChunk, {
            chunkId: embedding.chunkId,
          });

          if (chunk) {
            const brand = await ctx.runQuery(internal.brands.getBrand, {
              brandId: chunk.brandId,
            });

            if (brand) {
              results.push({
                brandUrl: brand.url,
                brandName: brand.name,
                content: chunk.text,
                relevanceScore: result._score,
              });
            }
          }
        }
      }

      return results;
    } catch (error) {
      console.error("Error searching brands:", error);
      throw new Error(`Failed to search brands: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Internal query to get brand embedding
export const getBrandEmbedding = query({
  args: {
    embeddingId: v.id("brand_embeddings"),
  },
  handler: async (ctx, { embeddingId }) => {
    return await ctx.db.get(embeddingId);
  },
});

// Internal query to get brand chunk
export const getBrandChunk = query({
  args: {
    chunkId: v.id("brand_chunks"),
  },
  handler: async (ctx, { chunkId }) => {
    return await ctx.db.get(chunkId);
  },
});

// Helper function to generate embeddings using OpenAI
async function generateEmbeddings(texts: string[]): Promise<number[][]> {
  if (texts.length === 0) return [];

  const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
  if (!OPENAI_API_KEY) {
    throw new Error("OPENAI_API_KEY environment variable is not set");
  }

  try {
    const response = await fetch("https://api.openai.com/v1/embeddings", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${OPENAI_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        input: texts,
        model: "text-embedding-ada-002",
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.data.map((item: any) => item.embedding);
  } catch (error) {
    throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
