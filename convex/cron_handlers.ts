import { internalAction } from "./_generated/server";
import { internal } from "./_generated/api";

// Process pending work automatically
export const processPendingWork = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log("🔄 Checking for pending work...");

    // Check for active batches that might be stuck
    const activeBatches = await ctx.runQuery(internal.csv_import.getActiveBatches);
    
    for (const batch of activeBatches) {
      console.log(`📋 Processing batch: ${batch.filename}`);
      
      // Check for pending brands in this batch
      const pendingBrands = await ctx.runQuery(internal.brands.getBrandsByBatch, {
        batchId: batch._id,
        status: "pending",
        limit: 10
      });

      if (pendingBrands.length > 0) {
        console.log(`🏢 Found ${pendingBrands.length} pending brands, starting processing...`);
        
        // Start brand processing for this batch
        await ctx.runAction(internal.csv_import.startBrandProcessing, {
          batchId: batch._id,
        });
      } else {
        // Check if all brands are completed and we need to start product scraping
        const allBrands = await ctx.runQuery(internal.brands.getBrandsByBatch, {
          batchId: batch._id,
          limit: 1000
        });

        const pendingCount = allBrands.filter(b => b.status === "pending").length;
        const processingCount = allBrands.filter(b => b.status === "processing").length;
        const completedCount = allBrands.filter(b => b.status === "completed").length;

        console.log(`📊 Batch ${batch.filename}: ${pendingCount} pending, ${processingCount} processing, ${completedCount} completed`);

        if (pendingCount === 0 && processingCount === 0 && completedCount > 0) {
          console.log(`✅ All brands completed for batch ${batch.filename}, checking product scraping...`);
          
          // Check if any brands need product scraping
          const brandsNeedingProducts = allBrands.filter(b => 
            b.status === "completed" && (!b.productCount || b.productCount === 0)
          );

          if (brandsNeedingProducts.length > 0) {
            console.log(`🛍️ Starting product scraping for ${brandsNeedingProducts.length} brands...`);
            await ctx.runAction(internal.csv_import.startProductScraping, {
              batchId: batch._id,
            });
          } else {
            console.log(`🎉 Batch ${batch.filename} is fully complete!`);
            // Mark batch as completed
            await ctx.runMutation(internal.csv_import.completeBatch, {
              batchId: batch._id,
            });
          }
        }
      }
    }

    console.log("✅ Pending work check completed");
  },
});

// Process pending product scraping
export const processPendingProducts = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log("🛍️ Checking for brands needing product scraping...");

    // Get brands that are completed but don't have products
    const brandsNeedingProducts = await ctx.runQuery(internal.product_scraper.getBrandsNeedingProductScraping, {
      limit: 10
    });

    if (brandsNeedingProducts.length > 0) {
      console.log(`🏪 Found ${brandsNeedingProducts.length} brands needing product scraping`);
      
      // Process them one by one with delays
      for (let i = 0; i < brandsNeedingProducts.length; i++) {
        const brand = brandsNeedingProducts[i];
        
        // Schedule with staggered timing (30 seconds apart)
        await ctx.scheduler.runAfter(i * 30000, internal.product_scraper.scrapeProductsForBrand, {
          brandId: brand._id,
        });
      }
    } else {
      console.log("✅ No brands need product scraping");
    }
  },
});

// Clean up old completed batches
export const cleanupOldBatches = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log("🧹 Cleaning up old completed batches...");

    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    
    // Get old completed batches
    const oldBatches = await ctx.runQuery(internal.csv_import.getOldCompletedBatches, {
      beforeDate: thirtyDaysAgo
    });

    console.log(`🗑️ Found ${oldBatches.length} old batches to clean up`);

    for (const batch of oldBatches) {
      // Archive the batch (mark as archived instead of deleting)
      await ctx.runMutation(internal.csv_import.archiveBatch, {
        batchId: batch._id,
      });
    }

    console.log("✅ Cleanup completed");
  },
});
