import { cronJobs } from "convex/server";
import { internal } from "./_generated/api";

const crons = cronJobs();

// Check for pending work every 5 minutes
crons.interval(
  "process-pending-work",
  { minutes: 5 },
  internal.cron_handlers.processPendingWork
);

// Check for brands needing product scraping every 10 minutes
crons.interval(
  "process-pending-products",
  { minutes: 10 },
  internal.cron_handlers.processPendingProducts
);

// Clean up old completed batches every day at 2 AM
crons.cron(
  "cleanup-old-batches",
  "0 2 * * *",
  internal.cron_handlers.cleanupOldBatches
);

export default crons;
