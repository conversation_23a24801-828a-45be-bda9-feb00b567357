import { v } from "convex/values";
import { mutation, query, action, internalMutation, internalAction, internalQuery } from "./_generated/server";
import { internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// Main internal action to scrape products for a brand
export const scrapeProductsForBrand = internalAction({
  args: {
    brandId: v.id("brands"),
  },
  handler: async (ctx, { brandId }) => {
    try {
      // Get brand details
      const brand = await ctx.runQuery(internal.brands.getBrand, { brandId });
      if (!brand) {
        throw new Error("Brand not found");
      }

      // Update brand status to processing products
      await ctx.runMutation(internal.product_scraper.updateBrandProductStatus, {
        brandId,
        status: "processing_products",
      });

      // Try to fetch products from /products.json
      const productsData = await fetchProductsFromUrl(brand.url);
      
      if (!productsData || productsData.length === 0) {
        await ctx.runMutation(internal.product_scraper.updateBrandProductStatus, {
          brandId,
          status: "no_products_found",
          errorMessage: "No products found at /products.json endpoint",
        });
        return { success: false, message: "No products found" };
      }

      // Process products in batches
      const batchSize = 10;
      let processedCount = 0;
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < productsData.length; i += batchSize) {
        const batch = productsData.slice(i, i + batchSize);
        
        const result = await ctx.runAction(internal.product_scraper.processBatchProducts, {
          brandId,
          products: batch,
          batchIndex: Math.floor(i / batchSize),
        });

        processedCount += result.processed;
        successCount += result.success;
        errorCount += result.errors;

        // Add delay between batches to avoid overwhelming the system
        if (i + batchSize < productsData.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Update brand with final status
      await ctx.runMutation(internal.product_scraper.updateBrandProductStatus, {
        brandId,
        status: "products_completed",
        productCount: successCount,
      });

      return {
        success: true,
        message: `Processed ${processedCount} products, ${successCount} successful, ${errorCount} errors`,
        stats: { processed: processedCount, success: successCount, errors: errorCount }
      };

    } catch (error) {
      await ctx.runMutation(internal.product_scraper.updateBrandProductStatus, {
        brandId,
        status: "products_failed",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      
      throw error;
    }
  },
});

// Internal action to process a batch of products
export const processBatchProducts = internalAction({
  args: {
    brandId: v.id("brands"),
    products: v.array(v.any()),
    batchIndex: v.number(),
  },
  handler: async (ctx, { brandId, products, batchIndex }) => {
    let processed = 0;
    let success = 0;
    let errors = 0;

    console.log(`Processing batch ${batchIndex} with ${products.length} products`);

    for (const productData of products) {
      try {
        processed++;
        
        // Map the product data to our schema
        const mappedProduct = mapProductData(productData, brandId);
        
        // Check if product already exists (by URL or SKU)
        const existing = await ctx.runQuery(internal.product_scraper.findExistingProduct, {
          brandId,
          url: mappedProduct.url,
          sku: mappedProduct.sku,
        });

        let productId: Id<"products">;
        
        if (existing) {
          // Update existing product
          productId = existing._id;
          await ctx.runMutation(internal.product_scraper.updateProduct, {
            productId,
            productData: mappedProduct,
          });
        } else {
          // Create new product
          productId = await ctx.runMutation(internal.product_scraper.createProduct, {
            productData: mappedProduct,
          });
        }

        // Generate embeddings for the product
        await ctx.runAction(internal.products.generateProductEmbeddings, {
          productId,
        });

        success++;
      } catch (error) {
        errors++;
        console.error(`Error processing product in batch ${batchIndex}:`, error);
      }
    }

    return { processed, success, errors };
  },
});

// Internal mutation to update brand product processing status
export const updateBrandProductStatus = internalMutation({
  args: {
    brandId: v.id("brands"),
    status: v.string(),
    errorMessage: v.optional(v.string()),
    productCount: v.optional(v.number()),
  },
  handler: async (ctx, { brandId, status, errorMessage, productCount }) => {
    const updateData: any = {
      updatedAt: Date.now(),
      lastScrapedAt: Date.now(),
    };

    // Update status in a custom field or use existing status field
    if (status.includes("products")) {
      updateData.productProcessingStatus = status;
    }

    if (errorMessage) {
      updateData.errorMessage = errorMessage;
    }

    if (productCount !== undefined) {
      updateData.productCount = productCount;
    }

    await ctx.db.patch(brandId, updateData);
  },
});

// Internal query to find existing product
export const findExistingProduct = internalQuery({
  args: {
    brandId: v.id("brands"),
    url: v.string(),
    sku: v.optional(v.string()),
  },
  handler: async (ctx, { brandId, url, sku }) => {
    // First try to find by URL
    const byUrl = await ctx.db
      .query("products")
      .withIndex("byUrl", (q) => q.eq("url", url))
      .first();
    
    if (byUrl) return byUrl;

    // If SKU is provided, try to find by SKU within the same brand
    if (sku) {
      const bySku = await ctx.db
        .query("products")
        .withIndex("bySku", (q) => q.eq("sku", sku))
        .filter((q) => q.eq(q.field("brandId"), brandId))
        .first();
      
      if (bySku) return bySku;
    }

    return null;
  },
});

// Internal mutation to create a new product
export const createProduct = internalMutation({
  args: {
    productData: v.any(),
  },
  handler: async (ctx, { productData }) => {
    const now = Date.now();
    return await ctx.db.insert("products", {
      ...productData,
      createdAt: now,
      updatedAt: now,
      lastScrapedAt: now,
    });
  },
});

// Internal mutation to update an existing product
export const updateProduct = internalMutation({
  args: {
    productId: v.id("products"),
    productData: v.any(),
  },
  handler: async (ctx, { productId, productData }) => {
    const { createdAt, ...updateData } = productData; // Don't update createdAt
    await ctx.db.patch(productId, {
      ...updateData,
      updatedAt: Date.now(),
      lastScrapedAt: Date.now(),
    });
  },
});

// Helper function to fetch products from URL
async function fetchProductsFromUrl(baseUrl: string): Promise<any[]> {
  try {
    // Clean the URL and add /products.json
    const cleanUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    const productsUrl = `${cleanUrl}/products.json`;
    
    console.log(`Fetching products from: ${productsUrl}`);
    
    const response = await fetch(productsUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ShopwizBot/1.0)',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Handle different response formats
    if (data.products && Array.isArray(data.products)) {
      return data.products;
    } else if (Array.isArray(data)) {
      return data;
    } else {
      console.warn('Unexpected products.json format:', data);
      return [];
    }
  } catch (error) {
    console.error(`Failed to fetch products from ${baseUrl}:`, error);
    return [];
  }
}

// Helper function to map external product data to our schema
function mapProductData(productData: any, brandId: Id<"brands">): any {
  const mapped: any = {
    brandId,
    name: productData.title || productData.name || 'Unknown Product',
    status: 'active',
  };

  // Map URL
  if (productData.handle) {
    mapped.url = `https://example.com/products/${productData.handle}`;
  } else if (productData.url) {
    mapped.url = productData.url;
  } else {
    mapped.url = `https://example.com/products/${Date.now()}`;
  }

  // Map basic fields
  if (productData.body_html || productData.description) {
    mapped.description = productData.body_html || productData.description;
  }
  
  if (productData.product_type || productData.category) {
    mapped.category = productData.product_type || productData.category;
  }

  if (productData.vendor || productData.brand) {
    mapped.brand = productData.vendor || productData.brand;
  }

  // Map SKU from variants or direct field
  if (productData.variants && productData.variants.length > 0) {
    mapped.sku = productData.variants[0].sku;
  } else if (productData.sku) {
    mapped.sku = productData.sku;
  }

  // Map pricing
  if (productData.variants && productData.variants.length > 0) {
    const variant = productData.variants[0];
    if (variant.price) {
      mapped.price = parseFloat(variant.price);
    }
    if (variant.compare_at_price) {
      mapped.compareAtPrice = parseFloat(variant.compare_at_price);
    }
  } else if (productData.price) {
    mapped.price = typeof productData.price === 'string' ? parseFloat(productData.price) : productData.price;
  }

  // Map images
  if (productData.images && productData.images.length > 0) {
    mapped.image = productData.images[0].src || productData.images[0];
    mapped.images = productData.images.map((img: any) => img.src || img);
  } else if (productData.featured_image) {
    mapped.image = productData.featured_image;
  }

  // Map tags
  if (productData.tags) {
    if (typeof productData.tags === 'string') {
      mapped.tags = productData.tags.split(',').map((tag: string) => tag.trim());
    } else if (Array.isArray(productData.tags)) {
      mapped.tags = productData.tags;
    }
  }

  // Map availability
  if (productData.available !== undefined) {
    mapped.availability = productData.available ? 'InStock' : 'OutOfStock';
  } else if (productData.variants && productData.variants.length > 0) {
    const variant = productData.variants[0];
    if (variant.available !== undefined) {
      mapped.availability = variant.available ? 'InStock' : 'OutOfStock';
    } else if (variant.inventory_quantity !== undefined) {
      mapped.availability = variant.inventory_quantity > 0 ? 'InStock' : 'OutOfStock';
      mapped.inventoryLevel = variant.inventory_quantity;
    }
  }

  return mapped;
}

// Internal action to trigger product scraping for multiple brands
export const scrapeProductsForBrands = internalAction({
  args: {
    brandIds: v.array(v.id("brands")),
  },
  handler: async (ctx, { brandIds }): Promise<{
    totalBrands: number;
    results: Array<{
      brandId: Id<"brands">;
      success: boolean;
      message: string;
      stats: { processed: number; success: number; errors: number };
    }>;
    summary: {
      successful: number;
      failed: number;
    };
  }> => {
    const results: Array<{
      brandId: Id<"brands">;
      success: boolean;
      message: string;
      stats: { processed: number; success: number; errors: number };
    }> = [];

    for (let i = 0; i < brandIds.length; i++) {
      const brandId = brandIds[i];

      try {
        const result = await ctx.runAction(internal.product_scraper.scrapeProductsForBrand, {
          brandId,
        });

        results.push({
          brandId,
          success: result.success,
          message: result.message,
          stats: result.stats,
        });

        // Add delay between brands to avoid overwhelming
        if (i < brandIds.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        results.push({
          brandId,
          success: false,
          message: error instanceof Error ? error.message : 'Unknown error',
          stats: { processed: 0, success: 0, errors: 1 },
        });
      }
    }

    return {
      totalBrands: brandIds.length,
      results,
      summary: {
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
      },
    };
  },
});

// Public action to scrape products for all brands with pending status
export const scrapeProductsForAllPendingBrands = action({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { limit = 50 }): Promise<{
    message?: string;
    totalBrands: number;
    results: Array<{
      brandId: Id<"brands">;
      success: boolean;
      message: string;
      stats: { processed: number; success: number; errors: number };
    }>;
    summary?: {
      successful: number;
      failed: number;
    };
  }> => {
    // Get brands that are completed but don't have products scraped yet
    const brands: Array<{ _id: Id<"brands"> }> = await ctx.runQuery(internal.product_scraper.getBrandsNeedingProductScraping, {
      limit,
    });

    if (brands.length === 0) {
      return {
        message: "No brands found that need product scraping",
        totalBrands: 0,
        results: [],
      };
    }

    const brandIds: Id<"brands">[] = brands.map((b: { _id: Id<"brands"> }) => b._id);
    return await ctx.runAction(internal.product_scraper.scrapeProductsForBrands, {
      brandIds,
    });
  },
});

// Internal query to get brands that need product scraping
export const getBrandsNeedingProductScraping = internalQuery({
  args: {
    limit: v.number(),
  },
  handler: async (ctx, { limit }) => {
    return await ctx.db
      .query("brands")
      .withIndex("byStatus", (q) => q.eq("status", "completed"))
      .filter((q) =>
        q.or(
          q.eq(q.field("productCount"), 0),
          q.eq(q.field("productCount"), undefined)
        )
      )
      .take(limit);
  },
});

// Query to get product scraping statistics
export const getProductScrapingStats = query({
  args: {},
  handler: async (ctx) => {
    const brands = await ctx.db.query("brands").collect();
    const products = await ctx.db.query("products").collect();

    const brandsWithProducts = brands.filter(b => (b.productCount || 0) > 0);
    const brandsNeedingScraping = brands.filter(b =>
      b.status === "completed" && (b.productCount || 0) === 0
    );

    return {
      totalBrands: brands.length,
      totalProducts: products.length,
      brandsWithProducts: brandsWithProducts.length,
      brandsNeedingScraping: brandsNeedingScraping.length,
      averageProductsPerBrand: brandsWithProducts.length > 0
        ? Math.round(products.length / brandsWithProducts.length)
        : 0,
    };
  },
});

// Query to get recent product scraping activity
export const getRecentProductActivity = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { limit = 20 }) => {
    return await ctx.db
      .query("products")
      .withIndex("byLastScraped", (q) => q.gt("lastScrapedAt", Date.now() - 24 * 60 * 60 * 1000)) // Last 24 hours
      .order("desc")
      .take(limit);
  },
});

// Public action to scrape products for selected brands
export const scrapeSelectedBrands = action({
  args: {
    brandIds: v.array(v.id("brands")),
  },
  handler: async (ctx, { brandIds }) => {
    return await ctx.runAction(internal.product_scraper.scrapeProductsForBrands, {
      brandIds,
    });
  },
});

// Public action to scrape products for a single brand
export const scrapeSingleBrand = action({
  args: {
    brandId: v.id("brands"),
  },
  handler: async (ctx, { brandId }) => {
    return await ctx.runAction(internal.product_scraper.scrapeProductsForBrand, {
      brandId,
    });
  },
});
