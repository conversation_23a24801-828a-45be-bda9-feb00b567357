import { v } from "convex/values";
import { mutation, query, action, internalMutation, internalAction, internalQuery } from "./_generated/server";
import { internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// CSV import for bulk brand URL processing
export const createImportBatch = internalMutation({
  args: {
    filename: v.string(),
    totalRows: v.number(),
  },
  handler: async (ctx, { filename, totalRows }) => {
    const batchId = await ctx.db.insert("import_batches", {
      filename,
      totalRows,
      processedRows: 0,
      successfulRows: 0,
      failedRows: 0,
      status: "processing",
      createdAt: Date.now(),
    });

    return batchId;
  },
});

// Get active batches to prevent concurrent processing
export const getActiveBatches = internalQuery({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("import_batches")
      .withIndex("byStatus", (q) => q.eq("status", "processing"))
      .collect();
  },
});

// Sequential brand processing - process 10 brands at a time
export const startBrandProcessing = internalAction({
  args: {
    batchId: v.string(),
  },
  handler: async (ctx, { batchId }) => {
    console.log(`Starting sequential brand processing for batch ${batchId}`);

    // Get all pending brands for this batch
    const brands = await ctx.runQuery(internal.brands.getBrandsByBatch, {
      batchId,
      status: "pending",
      limit: 10
    });

    if (brands.length === 0) {
      console.log(`No pending brands found for batch ${batchId}, checking if processing is complete`);

      // Check if all brands are processed
      const allBrands = await ctx.runQuery(internal.brands.getBrandsByBatch, {
        batchId,
        limit: 1000
      });

      const pendingCount = allBrands.filter(b => b.status === "pending").length;
      const processingCount = allBrands.filter(b => b.status === "processing").length;

      if (pendingCount === 0 && processingCount === 0) {
        console.log(`All brands processed for batch ${batchId}, starting product scraping`);
        // Start product scraping for completed brands
        await ctx.scheduler.runAfter(2000, internal.csv_import.startProductScraping, { batchId });
      }
      return;
    }

    console.log(`Processing ${brands.length} brands for batch ${batchId}`);

    // Process brands sequentially with delays
    for (let i = 0; i < brands.length; i++) {
      const brand = brands[i];

      // Schedule brand processing with staggered timing (5 seconds apart)
      await ctx.scheduler.runAfter(i * 5000, internal.brands.processBrand, {
        brandId: brand._id,
      });
    }

    // Schedule next batch of processing
    await ctx.scheduler.runAfter(brands.length * 5000 + 10000, internal.csv_import.startBrandProcessing, {
      batchId,
    });
  },
});

// Start product scraping after all brands are processed
export const startProductScraping = internalAction({
  args: {
    batchId: v.string(),
  },
  handler: async (ctx, { batchId }) => {
    console.log(`Starting product scraping for batch ${batchId}`);

    // Get all completed brands for this batch
    const brands = await ctx.runQuery(internal.brands.getBrandsByBatch, {
      batchId,
      status: "completed",
      limit: 10
    });

    if (brands.length === 0) {
      console.log(`No completed brands found for batch ${batchId}`);
      return;
    }

    console.log(`Starting product scraping for ${brands.length} brands`);

    // Process product scraping sequentially with delays
    for (let i = 0; i < brands.length; i++) {
      const brand = brands[i];

      // Schedule product scraping with staggered timing (10 seconds apart)
      await ctx.scheduler.runAfter(i * 10000, internal.product_scraper.scrapeProductsForBrand, {
        brandId: brand._id,
      });
    }

    // Schedule next batch of product scraping
    await ctx.scheduler.runAfter(brands.length * 10000 + 15000, internal.csv_import.startProductScraping, {
      batchId,
    });
  },
});

// Update batch progress
export const updateBatchProgress = internalMutation({
  args: {
    batchId: v.id("import_batches"),
    processedCount: v.number(),
    successCount: v.number(),
    failCount: v.number(),
    errors: v.array(v.string()),
  },
  handler: async (ctx, { batchId, processedCount, successCount, failCount, errors }) => {
    const batch = await ctx.db.get(batchId);
    if (!batch) return;

    const newProcessedRows = batch.processedRows + processedCount;
    const newSuccessfulRows = batch.successfulRows + successCount;
    const newFailedRows = batch.failedRows + failCount;
    
    const allErrors = [...(batch.errors || []), ...errors];
    
    // Check if batch is complete
    const isComplete = newProcessedRows >= batch.totalRows;
    
    await ctx.db.patch(batchId, {
      processedRows: newProcessedRows,
      successfulRows: newSuccessfulRows,
      failedRows: newFailedRows,
      errors: allErrors,
      status: isComplete ? "completed" : "processing",
      ...(isComplete && { completedAt: Date.now() }),
    });
  },
});

// Public mutation to create import batch (for frontend)
export const createImportBatchPublic = mutation({
  args: {
    filename: v.string(),
    totalRows: v.number(),
  },
  handler: async (ctx, { filename, totalRows }) => {
    const batchId = await ctx.db.insert("import_batches", {
      filename,
      totalRows,
      processedRows: 0,
      successfulRows: 0,
      failedRows: 0,
      status: "processing",
      createdAt: Date.now(),
    });

    return batchId;
  },
});

// Get import batch status
export const getImportBatch = query({
  args: {
    batchId: v.id("import_batches"),
  },
  handler: async (ctx, { batchId }) => {
    return await ctx.db.get(batchId);
  },
});

// List all import batches
export const listImportBatches = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { limit = 50 }) => {
    return await ctx.db
      .query("import_batches")
      .order("desc")
      .take(limit);
  },
});

// Process entire CSV file - Step 1: Save brands first
export const processCsvFile = action({
  args: {
    filename: v.string(),
    csvData: v.string(),
  },
  handler: async (ctx, { filename, csvData }): Promise<{
    batchId: Id<"import_batches">;
    totalUrls: number;
    newBrands: number;
    duplicates: number;
    message: string;
  }> => {
    // Check if there's already an active batch processing
    const activeBatches = await ctx.runQuery(internal.csv_import.getActiveBatches);
    if (activeBatches.length > 0) {
      throw new Error("Another CSV is currently being processed. Please wait for it to complete.");
    }
    // Parse CSV data
    const lines = csvData.trim().split('\n');

    // Handle single column CSV (just URLs)
    let urls: string[] = [];

    if (lines.length === 0) {
      throw new Error("CSV file is empty");
    }

    // Check if it's a single column CSV (no headers, just URLs)
    const firstLine = lines[0].trim();
    if (firstLine.startsWith('http') || (!firstLine.includes(',') && firstLine.includes('.'))) {
      // Single column CSV with just URLs
      urls = lines
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .map(url => url.startsWith('http') ? url : `https://${url}`);
    } else {
      // Multi-column CSV with headers
      const headers = lines[0].toLowerCase().split(',').map(h => h.trim());

      // Find URL column
      const urlColumnIndex = headers.findIndex(h =>
        h.includes('url') || h.includes('website') || h.includes('domain')
      );

      if (urlColumnIndex === -1) {
        throw new Error("No URL column found. Expected column names: url, website, or domain");
      }

      // Extract URLs
      for (let i = 1; i < lines.length; i++) {
        const columns = lines[i].split(',').map(c => c.trim().replace(/"/g, ''));
        const url = columns[urlColumnIndex];

        if (url && url.length > 0) {
          // Ensure URL has protocol
          const fullUrl = url.startsWith('http') ? url : `https://${url}`;
          urls.push(fullUrl);
        }
      }
    }

    if (urls.length === 0) {
      throw new Error("No valid URLs found in CSV file");
    }

    // Remove duplicates from the input
    const uniqueUrls = [...new Set(urls)];

    // Create import batch
    const batchId: Id<"import_batches"> = await ctx.runMutation(internal.csv_import.createImportBatch, {
      filename,
      totalRows: uniqueUrls.length,
    });

    // Step 1: Save all brands to database first (without processing)
    const result = await ctx.runAction(internal.csv_import.saveBrandsToDatabase, {
      batchId,
      urls: uniqueUrls,
    });

    // Step 2: Start sequential brand processing
    if (result.newBrands > 0) {
      await ctx.scheduler.runAfter(5000, internal.csv_import.startBrandProcessing, {
        batchId: batchId.toString(),
      });
    }

    return {
      batchId,
      totalUrls: uniqueUrls.length,
      newBrands: result.newBrands,
      duplicates: result.duplicates,
      message: `Saved ${result.newBrands} new brands, found ${result.duplicates} duplicates. Processing started for new brands.`,
    };
  },
});

// Step 1: Save brands to database without processing
export const saveBrandsToDatabase = internalAction({
  args: {
    batchId: v.id("import_batches"),
    urls: v.array(v.string()),
  },
  handler: async (ctx, { batchId, urls }) => {
    let newBrands = 0;
    let duplicates = 0;
    const newBrandIds: Id<"brands">[] = [];
    const errors: string[] = [];

    for (const url of urls) {
      try {
        // Validate URL
        new URL(url);

        // Check if brand already exists
        const existing = await ctx.runQuery(internal.brands.getBrandByUrl, { url });

        if (!existing) {
          // Create new brand (without scheduling processing yet)
          const brandId = await ctx.runMutation(internal.brands.createBrandFromImport, {
            url,
            batchId,
          });
          newBrandIds.push(brandId);
          newBrands++;
        } else {
          // Update existing brand's batch ID
          await ctx.runMutation(internal.brands.updateBrandBatch, {
            brandId: existing._id,
            batchId,
          });
          duplicates++;
        }
      } catch (error) {
        errors.push(`${url}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Update batch with initial results
    await ctx.runMutation(internal.csv_import.updateBatchProgress, {
      batchId,
      processedCount: urls.length,
      successCount: newBrands + duplicates,
      failCount: errors.length,
      errors,
    });

    return { newBrands, duplicates, errors };
  },
});



// Download sample CSV
export const downloadSampleCsv = mutation({
  args: {},
  handler: async (ctx) => {
    const sampleUrls = [
      "https://shopify.com",
      "https://stripe.com",
      "https://notion.so",
      "https://figma.com",
      "https://vercel.com",
      "https://github.com",
      "https://openai.com",
      "https://anthropic.com",
      "https://linear.app",
      "https://discord.com"
    ];

    return {
      filename: "sample-brands.csv",
      content: sampleUrls.join('\n'),
      mimeType: "text/csv"
    };
  },
});

// Get CSV import statistics
export const getImportStats = query({
  args: {},
  handler: async (ctx) => {
    const batches = await ctx.db.query("import_batches").collect();

    const stats = {
      totalBatches: batches.length,
      totalUrlsProcessed: batches.reduce((sum, b) => sum + b.processedRows, 0),
      totalSuccessful: batches.reduce((sum, b) => sum + b.successfulRows, 0),
      totalFailed: batches.reduce((sum, b) => sum + b.failedRows, 0),
      activeBatches: batches.filter(b => b.status === "processing").length,
      completedBatches: batches.filter(b => b.status === "completed").length,
    };

    return stats;
  },
});
