import { v } from "convex/values";
import { mutation, query, action, internalMutation, internalAction, internalQuery } from "./_generated/server";
import { internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// Query products with pagination and filters
export const listProducts = query({
  args: {
    brandId: v.optional(v.id("brands")),
    category: v.optional(v.string()),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("discontinued"))),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    search: v.optional(v.string()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, { brandId, category, status, minPrice, maxPrice, search, limit = 50, cursor }) => {
    let query = ctx.db.query("products");

    // Apply filters
    if (brandId) {
      const results = await ctx.db
        .query("products")
        .withIndex("byBrandId", (q) => q.eq("brandId", brandId))
        .order("desc")
        .take(limit + 1);

      return await processResults(ctx, results, limit);
    } else if (category) {
      const results = await ctx.db
        .query("products")
        .withIndex("byCategory", (q) => q.eq("category", category))
        .order("desc")
        .take(limit + 1);

      return await processResults(ctx, results, limit);
    } else if (status) {
      const results = await ctx.db
        .query("products")
        .withIndex("byStatus", (q) => q.eq("status", status))
        .order("desc")
        .take(limit + 1);

      return await processResults(ctx, results, limit);
    }

    // Default query
    const results = await ctx.db
      .query("products")
      .order("desc")
      .take(limit + 1);

    return await processResults(ctx, results, limit);
  },
});

// Helper function to process results
async function processResults(ctx: any, results: any[], limit: number) {
  // Apply additional filters (this would be done in the query in a real implementation)
  // For now, we'll just handle pagination

  const hasMore = results.length > limit;
  if (hasMore) {
    results = results.slice(0, limit);
  }

  // Get brand information for each product
  const productsWithBrands = await Promise.all(
    results.map(async (product) => {
      const brand = await ctx.db.get(product.brandId);
      return {
        ...product,
        brandName: brand?.name || brand?.url || "Unknown",
        brandUrl: brand?.url,
      };
    })
  );

  return {
    products: productsWithBrands,
    hasMore,
    nextCursor: hasMore ? results[results.length - 1]._id : null,
  };
}



// Simple query to get all products as an array (for UI components)
export const getAllProducts = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { limit = 1000 }) => {
    const products = await ctx.db
      .query("products")
      .order("desc")
      .take(limit);

    // Get brand information for each product
    const productsWithBrands = await Promise.all(
      products.map(async (product) => {
        const brand = await ctx.db.get(product.brandId);
        return {
          ...product,
          brandName: brand?.name || brand?.url || "Unknown",
          brandUrl: brand?.url,
        };
      })
    );

    return productsWithBrands;
  },
});

// Get product statistics
export const getProductStats = query({
  args: {},
  handler: async (ctx) => {
    const products = await ctx.db.query("products").collect();
    
    const stats = {
      total: products.length,
      active: products.filter(p => p.status === "active").length,
      inactive: products.filter(p => p.status === "inactive").length,
      discontinued: products.filter(p => p.status === "discontinued").length,
      withPrices: products.filter(p => p.price !== undefined).length,
      avgPrice: products.filter(p => p.price !== undefined).reduce((sum, p) => sum + (p.price || 0), 0) / products.filter(p => p.price !== undefined).length || 0,
      categories: [...new Set(products.map(p => p.category).filter(Boolean))].length,
      brands: [...new Set(products.map(p => p.brandId))].length,
    };

    return stats;
  },
});

// Search products using vector search
export const searchProducts = action({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
    brandId: v.optional(v.id("brands")),
  },
  handler: async (ctx, { query, limit = 20, brandId }): Promise<Array<any>> => {
    try {
      // Generate embedding for search query
      const [queryEmbedding] = await generateEmbeddings([query]);
      
      // Perform vector search
      const searchResults = await ctx.vectorSearch("product_embeddings", "byEmbedding", {
        vector: queryEmbedding,
        limit: limit * 2, // Get more results to filter
      });

      // Get products and filter by brand if specified
      const results: any[] = [];
      for (const result of searchResults) {
        const embedding = await ctx.runQuery(internal.products.getProductEmbedding, {
          embeddingId: result._id,
        });

        if (embedding) {
          const product = await ctx.runQuery(internal.products.getProduct, {
            productId: embedding.productId,
          });

          if (product && (!brandId || product.brandId === brandId)) {
            const brand = await ctx.runQuery(internal.brands.getBrand, {
              brandId: product.brandId,
            });
            
            results.push({
              ...product,
              brandName: brand?.name || brand?.url || "Unknown",
              brandUrl: brand?.url,
              relevanceScore: result._score,
            });
          }
        }
        
        if (results.length >= limit) break;
      }

      return results;
    } catch (error) {
      console.error("Error searching products:", error);
      throw new Error(`Failed to search products: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Add product
export const addProduct = mutation({
  args: {
    brandId: v.id("brands"),
    url: v.string(),
    name: v.string(),
    description: v.optional(v.string()),
    price: v.optional(v.number()),
    priceCurrency: v.optional(v.string()),
    category: v.optional(v.string()),
    sku: v.optional(v.string()),
    image: v.optional(v.string()),
    availability: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const productId = await ctx.db.insert("products", {
      ...args,
      status: "active",
      createdAt: now,
      updatedAt: now,
    });

    // Generate embeddings for the product
    await ctx.scheduler.runAfter(0, internal.products.generateProductEmbeddings, {
      productId,
    });

    // Update brand product count
    const brand = await ctx.db.get(args.brandId);
    if (brand) {
      await ctx.db.patch(args.brandId, {
        productCount: (brand.productCount || 0) + 1,
        updatedAt: now,
      });
    }

    return productId;
  },
});

// Generate embeddings for a product
export const generateProductEmbeddings = internalAction({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, { productId }) => {
    const product = await ctx.runQuery(internal.products.getProduct, { productId });
    if (!product) return;

    // Create text for embedding
    const textParts = [
      product.name,
      product.description,
      product.category,
      product.brand,
    ].filter(Boolean);
    
    const textForEmbedding = textParts.join(" ");
    
    if (textForEmbedding.trim().length === 0) return;

    try {
      const [embedding] = await generateEmbeddings([textForEmbedding]);
      
      await ctx.runMutation(internal.products.storeProductEmbedding, {
        productId,
        embedding,
        textSource: textForEmbedding,
      });
    } catch (error) {
      console.error(`Failed to generate embedding for product ${productId}:`, error);
    }
  },
});

// Store product embedding
export const storeProductEmbedding = internalMutation({
  args: {
    productId: v.id("products"),
    embedding: v.array(v.number()),
    textSource: v.string(),
  },
  handler: async (ctx, { productId, embedding, textSource }) => {
    return await ctx.db.insert("product_embeddings", {
      productId,
      embedding,
      model: "text-embedding-3-small",
      textSource,
      createdAt: Date.now(),
    });
  },
});

// Internal queries
export const getProduct = internalQuery({
  args: { productId: v.id("products") },
  handler: async (ctx, { productId }) => {
    return await ctx.db.get(productId);
  },
});

export const getProductEmbedding = internalQuery({
  args: { embeddingId: v.id("product_embeddings") },
  handler: async (ctx, { embeddingId }) => {
    return await ctx.db.get(embeddingId);
  },
});



// Helper function to generate embeddings
async function generateEmbeddings(texts: string[]): Promise<number[][]> {
  if (texts.length === 0) return [];
  
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
  if (!OPENAI_API_KEY) {
    throw new Error("OPENAI_API_KEY environment variable is not set");
  }

  try {
    const response = await fetch("https://api.openai.com/v1/embeddings", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${OPENAI_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        input: texts,
        model: "text-embedding-3-small",
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.data.map((item: any) => item.embedding);
  } catch (error) {
    throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
