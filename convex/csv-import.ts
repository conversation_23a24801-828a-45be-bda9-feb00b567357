import { v } from "convex/values";
import { mutation, action, internalMutation, internalAction } from "./_generated/server";
import { internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// CSV import for bulk brand URL processing
export const createImportBatch = mutation({
  args: {
    filename: v.string(),
    totalRows: v.number(),
  },
  handler: async (ctx, { filename, totalRows }) => {
    const batchId = await ctx.db.insert("import_batches", {
      filename,
      totalRows,
      processedRows: 0,
      successfulRows: 0,
      failedRows: 0,
      status: "processing",
      createdAt: Date.now(),
    });
    
    return batchId;
  },
});

// Process CSV data in chunks for performance
export const processCsvChunk = internalAction({
  args: {
    batchId: v.id("import_batches"),
    urls: v.array(v.string()),
    chunkIndex: v.number(),
  },
  handler: async (ctx, { batchId, urls, chunkIndex }) => {
    console.log(`Processing chunk ${chunkIndex} with ${urls.length} URLs`);
    
    let successCount = 0;
    let failCount = 0;
    const errors: string[] = [];

    for (const url of urls) {
      try {
        // Validate URL
        new URL(url);
        
        // Check if brand already exists
        const existing = await ctx.runQuery(internal.brands.getBrandByUrl, { url });
        
        if (!existing) {
          // Create new brand
          const brandId = await ctx.runMutation(internal.brands.createBrandFromImport, {
            url,
            batchId: batchId,
          });
          
          // Schedule processing
          await ctx.scheduler.runAfter(0, internal.brands.processBrand, { brandId });
          successCount++;
        } else {
          // Update existing brand's batch ID
          await ctx.runMutation(internal.brands.updateBrandBatch, {
            brandId: existing._id,
            batchId,
          });
          successCount++;
        }
      } catch (error) {
        failCount++;
        errors.push(`${url}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Update batch progress
    await ctx.runMutation(internal["csv-import"].updateBatchProgress, {
      batchId,
      processedCount: urls.length,
      successCount,
      failCount,
      errors,
    });

    return { successCount, failCount, errors };
  },
});

// Update batch progress
export const updateBatchProgress = internalMutation({
  args: {
    batchId: v.id("import_batches"),
    processedCount: v.number(),
    successCount: v.number(),
    failCount: v.number(),
    errors: v.array(v.string()),
  },
  handler: async (ctx, { batchId, processedCount, successCount, failCount, errors }) => {
    const batch = await ctx.db.get(batchId);
    if (!batch) return;

    const newProcessedRows = batch.processedRows + processedCount;
    const newSuccessfulRows = batch.successfulRows + successCount;
    const newFailedRows = batch.failedRows + failCount;
    
    const allErrors = [...(batch.errors || []), ...errors];
    
    // Check if batch is complete
    const isComplete = newProcessedRows >= batch.totalRows;
    
    await ctx.db.patch(batchId, {
      processedRows: newProcessedRows,
      successfulRows: newSuccessfulRows,
      failedRows: newFailedRows,
      errors: allErrors,
      status: isComplete ? "completed" : "processing",
      ...(isComplete && { completedAt: Date.now() }),
    });
  },
});

// Get import batch status
export const getImportBatch = mutation({
  args: {
    batchId: v.id("import_batches"),
  },
  handler: async (ctx, { batchId }) => {
    return await ctx.db.get(batchId);
  },
});

// List all import batches
export const listImportBatches = mutation({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { limit = 50 }) => {
    return await ctx.db
      .query("import_batches")
      .order("desc")
      .take(limit);
  },
});

// Process entire CSV file
export const processCsvFile = action({
  args: {
    filename: v.string(),
    csvData: v.string(),
  },
  handler: async (ctx, { filename, csvData }) => {
    // Parse CSV data
    const lines = csvData.trim().split('\n');
    const headers = lines[0].toLowerCase().split(',').map(h => h.trim());
    
    // Find URL column
    const urlColumnIndex = headers.findIndex(h => 
      h.includes('url') || h.includes('website') || h.includes('domain')
    );
    
    if (urlColumnIndex === -1) {
      throw new Error("No URL column found. Expected column names: url, website, or domain");
    }

    // Extract URLs
    const urls: string[] = [];
    for (let i = 1; i < lines.length; i++) {
      const columns = lines[i].split(',').map(c => c.trim().replace(/"/g, ''));
      const url = columns[urlColumnIndex];
      
      if (url && url.length > 0) {
        // Ensure URL has protocol
        const fullUrl = url.startsWith('http') ? url : `https://${url}`;
        urls.push(fullUrl);
      }
    }

    if (urls.length === 0) {
      throw new Error("No valid URLs found in CSV file");
    }

    // Create import batch
    const batchId = await ctx.runMutation(internal["csv-import"].createImportBatch, {
      filename,
      totalRows: urls.length,
    });

    // Process in chunks of 50 for performance
    const chunkSize = 50;
    const chunks = [];
    for (let i = 0; i < urls.length; i += chunkSize) {
      chunks.push(urls.slice(i, i + chunkSize));
    }

    // Schedule chunk processing
    for (let i = 0; i < chunks.length; i++) {
      await ctx.scheduler.runAfter(i * 1000, internal["csv-import"].processCsvChunk, {
        batchId,
        urls: chunks[i],
        chunkIndex: i,
      });
    }

    return {
      batchId,
      totalUrls: urls.length,
      totalChunks: chunks.length,
      message: `Started processing ${urls.length} URLs in ${chunks.length} chunks`,
    };
  },
});

// Get CSV import statistics
export const getImportStats = mutation({
  args: {},
  handler: async (ctx) => {
    const batches = await ctx.db.query("import_batches").collect();
    
    const stats = {
      totalBatches: batches.length,
      totalUrlsProcessed: batches.reduce((sum, b) => sum + b.processedRows, 0),
      totalSuccessful: batches.reduce((sum, b) => sum + b.successfulRows, 0),
      totalFailed: batches.reduce((sum, b) => sum + b.failedRows, 0),
      activeBatches: batches.filter(b => b.status === "processing").length,
      completedBatches: batches.filter(b => b.status === "completed").length,
    };

    return stats;
  },
});
