import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Demo function to add sample brands for testing
export const addDemoBrands = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🎭 Adding demo brands for testing...");

    const demoBrands = [
      {
        url: "https://shopify.com",
        name: "Shopify",
        description: "E-commerce platform for online stores and retail point-of-sale systems",
      },
      {
        url: "https://stripe.com", 
        name: "Stripe",
        description: "Online payment processing for internet businesses",
      },
      {
        url: "https://notion.so",
        name: "Notion",
        description: "All-in-one workspace for notes, tasks, wikis, and databases",
      },
      {
        url: "https://figma.com",
        name: "Figma", 
        description: "Collaborative interface design tool",
      },
      {
        url: "https://vercel.com",
        name: "Vercel",
        description: "Platform for frontend frameworks and static sites",
      },
    ];

    const brandIds = [];

    for (const brand of demoBrands) {
      // Check if brand already exists
      const existing = await ctx.db
        .query("brands")
        .withIndex("byUrl", (q) => q.eq("url", brand.url))
        .first();

      if (!existing) {
        const brandId = await ctx.db.insert("brands", {
          url: brand.url,
          name: brand.name,
          status: "pending",
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        // Add some demo content
        const contentId = await ctx.db.insert("brand_content", {
          brandId,
          contentType: "homepage",
          rawContent: `${brand.name} - ${brand.description}. This is a leading technology company that provides innovative solutions for businesses and developers worldwide.`,
          title: brand.name,
          description: brand.description,
          extractedAt: Date.now(),
        });

        // Add demo chunks
        const chunks = [
          `${brand.name} is a technology company.`,
          `${brand.description}`,
          `${brand.name} provides solutions for businesses and developers.`,
        ];

        for (let i = 0; i < chunks.length; i++) {
          await ctx.db.insert("brand_chunks", {
            brandId,
            contentId,
            text: chunks[i],
            chunkIndex: i,
            chunkType: "content",
            embeddingId: null,
          });
        }

        brandIds.push(brandId);
        console.log(`✅ Added demo brand: ${brand.name}`);
      } else {
        console.log(`⏭️ Skipped existing brand: ${brand.name}`);
      }
    }

    console.log(`🎉 Demo setup complete! Added ${brandIds.length} new brands.`);
    return {
      success: true,
      message: `Added ${brandIds.length} demo brands. Note: These have demo content but no embeddings yet. Use the AI assistant to process them fully.`,
      brandIds,
    };
  },
});

// Function to clear all demo data
export const clearDemoData = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🧹 Clearing demo data...");

    const demoBrandUrls = [
      "https://shopify.com",
      "https://stripe.com", 
      "https://notion.so",
      "https://figma.com",
      "https://vercel.com",
    ];

    let deletedCount = 0;

    for (const url of demoBrandUrls) {
      const brand = await ctx.db
        .query("brands")
        .withIndex("byUrl", (q) => q.eq("url", url))
        .first();

      if (brand) {
        // Delete related content
        const content = await ctx.db
          .query("brand_content")
          .withIndex("byBrandId", (q) => q.eq("brandId", brand._id))
          .collect();

        for (const c of content) {
          await ctx.db.delete(c._id);
        }

        // Delete related chunks
        const chunks = await ctx.db
          .query("brand_chunks")
          .withIndex("byBrandId", (q) => q.eq("brandId", brand._id))
          .collect();

        for (const chunk of chunks) {
          // Delete related embeddings
          if (chunk.embeddingId) {
            await ctx.db.delete(chunk.embeddingId);
          }
          await ctx.db.delete(chunk._id);
        }

        // Delete the brand
        await ctx.db.delete(brand._id);
        deletedCount++;
        console.log(`🗑️ Deleted demo brand: ${brand.name || url}`);
      }
    }

    console.log(`✅ Cleared ${deletedCount} demo brands and related data.`);
    return {
      success: true,
      message: `Cleared ${deletedCount} demo brands and all related data.`,
      deletedCount,
    };
  },
});
