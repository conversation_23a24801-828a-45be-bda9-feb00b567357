import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

// Schema.org compliant e-commerce monitoring system
// Designed for 100k+ brands with high performance and data integrity
export default defineSchema({

  // Core brand entity following Schema.org/Brand
  brands: defineTable({
    // Basic identifiers
    url: v.string(),
    name: v.optional(v.string()),
    status: v.union(v.literal("pending"), v.literal("processing"), v.literal("completed"), v.literal("failed")),

    // Schema.org Brand properties
    logo: v.optional(v.string()),
    description: v.optional(v.string()),
    foundingDate: v.optional(v.string()),
    founder: v.optional(v.string()),
    headquarters: v.optional(v.string()),
    industry: v.optional(v.string()),
    numberOfEmployees: v.optional(v.number()),
    website: v.optional(v.string()),

    // Contact information
    email: v.optional(v.string()),
    telephone: v.optional(v.string()),

    // Social media
    sameAs: v.optional(v.array(v.string())), // Social media URLs

    // Business details
    legalName: v.optional(v.string()),
    taxID: v.optional(v.string()),
    duns: v.optional(v.string()),

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
    lastScrapedAt: v.optional(v.number()),

    // Processing
    errorMessage: v.optional(v.string()),
    retryCount: v.optional(v.number()),

    // Metrics
    productCount: v.optional(v.number()),
    avgProductPrice: v.optional(v.number()),

    // CSV import tracking
    importBatchId: v.optional(v.string()),
  }).index("byUrl", ["url"])
    .index("byStatus", ["status"])
    .index("byName", ["name"])
    .index("byIndustry", ["industry"])
    .index("byImportBatch", ["importBatchId"])
    .index("byLastScraped", ["lastScrapedAt"]),

  // Products following Schema.org/Product
  products: defineTable({
    // Core identifiers
    brandId: v.id("brands"),
    url: v.string(),
    sku: v.optional(v.string()),
    gtin: v.optional(v.string()), // Global Trade Item Number
    mpn: v.optional(v.string()), // Manufacturer Part Number

    // Basic product info
    name: v.string(),
    description: v.optional(v.string()),
    category: v.optional(v.string()),

    // Schema.org Product properties
    brand: v.optional(v.string()),
    model: v.optional(v.string()),
    color: v.optional(v.string()),
    size: v.optional(v.string()),
    weight: v.optional(v.string()),
    material: v.optional(v.string()),

    // Pricing (Schema.org/Offer)
    price: v.optional(v.number()),
    priceCurrency: v.optional(v.string()),
    priceValidUntil: v.optional(v.string()),
    availability: v.optional(v.string()), // InStock, OutOfStock, PreOrder, etc.

    // Images
    image: v.optional(v.string()),
    images: v.optional(v.array(v.string())),

    // Reviews and ratings
    aggregateRating: v.optional(v.object({
      ratingValue: v.number(),
      reviewCount: v.number(),
      bestRating: v.optional(v.number()),
      worstRating: v.optional(v.number()),
    })),

    // Inventory
    inventoryLevel: v.optional(v.number()),
    lowStockThreshold: v.optional(v.number()),

    // Dimensions
    width: v.optional(v.string()),
    height: v.optional(v.string()),
    depth: v.optional(v.string()),

    // Additional properties
    keywords: v.optional(v.array(v.string())),
    tags: v.optional(v.array(v.string())),

    // Status and processing
    status: v.union(v.literal("active"), v.literal("inactive"), v.literal("discontinued")),
    isVariant: v.optional(v.boolean()),
    parentProductId: v.optional(v.id("products")),

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
    lastScrapedAt: v.optional(v.number()),

    // Processing
    errorMessage: v.optional(v.string()),
  }).index("byBrandId", ["brandId"])
    .index("byUrl", ["url"])
    .index("bySku", ["sku"])
    .index("byGtin", ["gtin"])
    .index("byName", ["name"])
    .index("byCategory", ["category"])
    .index("byStatus", ["status"])
    .index("byPrice", ["price"])
    .index("byLastScraped", ["lastScrapedAt"])
    .index("byParentProduct", ["parentProductId"]),

  // CSV import batches for tracking bulk uploads
  import_batches: defineTable({
    filename: v.string(),
    totalRows: v.number(),
    processedRows: v.number(),
    successfulRows: v.number(),
    failedRows: v.number(),
    status: v.union(v.literal("processing"), v.literal("completed"), v.literal("failed")),
    errors: v.optional(v.array(v.string())),
    createdAt: v.number(),
    completedAt: v.optional(v.number()),
  }).index("byStatus", ["status"])
    .index("byCreatedAt", ["createdAt"]),

  // Content storage for RAG (optimized for 100k+ brands)
  brand_content: defineTable({
    brandId: v.id("brands"),
    contentType: v.union(v.literal("homepage"), v.literal("about"), v.literal("products"), v.literal("contact")),
    rawContent: v.string(),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    extractedAt: v.number(),
    contentHash: v.optional(v.string()), // For deduplication
  }).index("byBrandId", ["brandId"])
    .index("byContentType", ["contentType"])
    .index("byContentHash", ["contentHash"]),

  // Chunked content for embeddings (optimized for vector search)
  brand_chunks: defineTable({
    brandId: v.id("brands"),
    contentId: v.id("brand_content"),
    text: v.string(),
    chunkIndex: v.number(),
    chunkType: v.union(v.literal("title"), v.literal("description"), v.literal("content"), v.literal("metadata")),
    embeddingId: v.union(v.id("brand_embeddings"), v.null()),
    tokenCount: v.optional(v.number()),
  }).index("byBrandId", ["brandId"])
    .index("byContentId", ["contentId"])
    .index("byEmbeddingId", ["embeddingId"])
    .index("byChunkType", ["chunkType"]),

  // Vector embeddings (optimized for 100k+ scale)
  brand_embeddings: defineTable({
    chunkId: v.id("brand_chunks"),
    embedding: v.array(v.number()),
    model: v.string(),
    createdAt: v.number(),
    dimensions: v.number(),
  }).index("byChunkId", ["chunkId"])
    .index("byModel", ["model"])
    .vectorIndex("byEmbedding", {
      vectorField: "embedding",
      dimensions: 1536, // OpenAI text-embedding-3-small
    }),

  // Product embeddings for product search
  product_embeddings: defineTable({
    productId: v.id("products"),
    embedding: v.array(v.number()),
    model: v.string(),
    createdAt: v.number(),
    textSource: v.string(), // What text was embedded (name, description, etc.)
  }).index("byProductId", ["productId"])
    .index("byModel", ["model"])
    .vectorIndex("byEmbedding", {
      vectorField: "embedding",
      dimensions: 1536,
    }),

  // System metrics for monitoring 100k+ brands
  system_metrics: defineTable({
    metricType: v.string(),
    value: v.number(),
    timestamp: v.number(),
    metadata: v.optional(v.object({
      brandCount: v.optional(v.number()),
      productCount: v.optional(v.number()),
      processingRate: v.optional(v.number()),
      errorRate: v.optional(v.number()),
    })),
  }).index("byMetricType", ["metricType"])
    .index("byTimestamp", ["timestamp"]),
});
